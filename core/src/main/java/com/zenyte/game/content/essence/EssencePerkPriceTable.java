package com.zenyte.game.content.essence;

public class EssencePerkPriceTable {
    /*
     * Combat Perks
     */
    public static final int v_MeleeISpeed = 100;
    public static final int v_MeleeIIAccuracy = 1000;
    public static final int v_MeleeIIIDamage = 2500;
    public static final int v_RangedISpeed = 100;
    public static final int v_RangedIIAccuracy = 1000;
    public static final int v_RangedIIIDamage = 2500;
    public static final int v_MagicISpeed = 100;
    public static final int v_MagicIIAccuracy = 1000;
    public static final int v_MagicIIIDamage = 2500;
    public static final int v_HolyInterventionI = 1000;
    public static final int v_HolyInterventionII = 5000;
    public static final int v_HolyInterventionIII = 10000;
    public static final int v_MeleeLifeLeech = 3000;
    public static final int v_RangedLifeLeech = 3000;
    public static final int v_MagicLifeLeech = 3000;
    public static final int v_CrushWeaponryMaster = 1000;
    public static final int v_StabWeaponryMaster = 1000;
    public static final int v_SlashWeaponryMaster = 1000;
    public static final int v_RuneSaver = 1000;
    public static final int v_AmmoSaver = 1000;
    public static final int v_Berserker = 5000;
    public static final int v_Recoiled = 500;
    public static final int v_TasteMoreVengeance = 1000;
    public static final int v_SpecialRegen = 1000;
    public static final int v_MinionsMight = 1000;
    public static final int v_SoulStealer = 7500;
    public static final int v_SoulTiedTwistedBow = 30000;
    public static final int v_SoulTiedScytheOfVitur = 30000;
    public static final int v_SoulTiedTumekensShadow = 30000;
    /*
     * Utility Perks
     */
    public static final int v_BarbarianFisher = 1500;
    public static final int v_BoneCruncher = 4500;
    public static final int v_BountifulSacrifice = 2000;
    public static final int v_MasterOfTheCraft = 2200;
    public static final int v_FirstImpressions = 1000;
    public static final int v_RunForrestRun = 800;
    public static final int v_IWantItAll = 2500;
    public static final int v_SousChef = 3500;
    public static final int v_Woodsman = 2000;
    public static final int v_DoubleLogs = 300;
    public static final int v_Pyromaniac = 1100;
    public static final int v_DoublePyro = 500;
    public static final int v_Mixologist = 1500;
    public static final int v_MinerFortyNiner = 2000;
    public static final int v_DoubleOre = 300;
    public static final int v_Botanist = 2200;
    public static final int v_TrackStar = 1500;
    public static final int v_SwissArmyMan = 2000;
    public static final int v_SleightOfHand = 2500;
    public static final int v_BetterThief = 600;
    public static final int v_Alchoholic = 500;
    public static final int v_AshesToAshes = 2000;
    public static final int v_DoubleChins = 4500;
    public static final int v_FarmersFortune = 2500;
    public static final int v_Fertilizer = 600;
    public static final int v_TheLegendaryFisherman = 2000;
    public static final int v_DoubleFish = 300;
    public static final int v_ClueSkipper = 1300;
    public static final int v_ClueStepMinimizer = 12000;
    public static final int v_ClueCollector = 2600;
    public static final int v_UnholyIntervention = 4000;
    public static final int v_SlayersFavor = 4500;
    public static final int v_ContractKiller = 6000;
    public static final int v_IgnoranceIsBliss = 5000;
    public static final int v_InfallibleShackles = 5500;
    public static final int v_BrawnOfJustice = 7500;
    public static final int v_VigourOfInquisition = 25000;
    public static final int v_TheRedeemer = 3000;
    public static final int v_HoleyMoley = 1500;
    public static final int v_SustainedAggression = 2500;
    public static final int v_ArcaneKnowledge = 15000;
    public static final int v_BurnBabyBurn = 2000;
    public static final int v_Backfire = 5500;
    public static final int v_IVotedI = 1000;
    public static final int v_IVotedII = 5000;
    public static final int v_IVotedIII = 10000;
    public static final int v_Locksmith = 2500;
    public static final int v_DoubleTap = 2500;
    public static final int v_CorporealScrutiny = 8000;
    public static final int v_HoarderMentality = 1500;
    public static final int v_IceForTheEyeless = 2800;
    public static final int v_DagannothPeasants = 3000;
    public static final int v_SlayersSpite = 1800;
    public static final int v_HammerDown = 6000;
    public static final int v_AnimalTamer = 5000;
    public static final int v_FamiliarsFortune = 1200;
    public static final int v_NoPetDebt = 10000;
    public static final int v_CrystalCatalyst = 4000;
    public static final int v_LethalAttunement = 3200;
    public static final int v_RevItUp = 1800;
    public static final int v_SlayersSovereignty = 6000;
    public static final int v_SlayerPointBonus = 3800;
    public static final int v_DullAxes = 7000;
    public static final int v_EyeDontSeeYou = 5000;
    public static final int v_AllGassedUp = 8000;
    public static final int v_BarrowsMazeMaster = 1500;
    public static final int v_CryptKeeper = 2500;
    public static final int v_GoldenLuck = 30000;
    public static final int v_DiamondLuck = 50000;
    public static final int v_EssenceMagnet = 300;
    /*
     * Unused
     */
    public static final int v_NoOnesHome = 2500;
    public static final int v_SliceNDice = 8000;
    public static final int v_Enlightened = 50000;
    public static final int v_Enraged = 50000;
    public static final int v_HolierThanThou = 1500;
    public static final int v_JabbasRightHand = 12500;
}
